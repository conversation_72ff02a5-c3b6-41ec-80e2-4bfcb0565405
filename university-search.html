<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>University Search - StudyAbroad Pro</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="search-styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-graduation-cap"></i>
                <span>StudyAbroad Pro</span>
            </div>
            <ul class="nav-menu">
                <li><a href="index.html">Home</a></li>
                <li><a href="index.html#services">Services</a></li>
                <li><a href="index.html#testimonials">Testimonials</a></li>
                <li><a href="index.html#contact">Contact</a></li>
                <li><a href="index.html#register" class="nav-cta">Get Started</a></li>
            </ul>
            <div class="hamburger">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- Search Header -->
    <section class="search-header">
        <div class="container">
            <div class="search-header-content">
                <h1>Find Your Perfect University</h1>
                <p>Choose your preferred search method to discover universities tailored to your profile</p>

                <!-- Search Mode Toggle -->
                <div class="search-mode-toggle">
                    <button class="mode-btn active" data-mode="ai" onclick="switchSearchMode('ai')">
                        <i class="fas fa-robot"></i>
                        <span>AI Match</span>
                        <small>Get personalized recommendations</small>
                    </button>
                    <button class="mode-btn" data-mode="manual" onclick="switchSearchMode('manual')">
                        <i class="fas fa-search"></i>
                        <span>Manual Search</span>
                        <small>Browse and filter universities</small>
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- AI Matching Form -->
    <section class="ai-matching" id="aiMatching">
        <div class="container">
            <div class="ai-form-container">
                <div class="ai-form-header">
                    <div class="ai-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                    <h2>AI University Matching</h2>
                    <p>Tell us about your academic profile and we'll find the best universities for you</p>
                </div>

                <form class="ai-form" id="aiForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="gpa">GPA / Percentage</label>
                            <input type="number" id="gpa" name="gpa" placeholder="3.5 or 85%" step="0.01" min="0" max="4" required>
                        </div>
                        <div class="form-group">
                            <label for="greScore">GRE Score (Optional)</label>
                            <input type="number" id="greScore" name="greScore" placeholder="320" min="260" max="340">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="ieltsScore">IELTS/TOEFL Score (Optional)</label>
                            <input type="text" id="ieltsScore" name="ieltsScore" placeholder="7.5 IELTS or 100 TOEFL">
                        </div>
                        <div class="form-group">
                            <label for="workExp">Work Experience (Years)</label>
                            <select id="workExp" name="workExp">
                                <option value="0">No Experience</option>
                                <option value="1">1-2 Years</option>
                                <option value="3">3-5 Years</option>
                                <option value="6">6+ Years</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="preferredCountry">Preferred Country (Optional)</label>
                            <select id="preferredCountry" name="preferredCountry">
                                <option value="">No Preference</option>
                                <option value="usa">United States</option>
                                <option value="uk">United Kingdom</option>
                                <option value="canada">Canada</option>
                                <option value="australia">Australia</option>
                                <option value="germany">Germany</option>
                                <option value="netherlands">Netherlands</option>
                                <option value="singapore">Singapore</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="studyField">Field of Study</label>
                            <select id="studyField" name="studyField" required>
                                <option value="">Select Field</option>
                                <option value="engineering">Engineering</option>
                                <option value="computer-science">Computer Science</option>
                                <option value="business">Business</option>
                                <option value="medicine">Medicine</option>
                                <option value="arts">Arts & Humanities</option>
                                <option value="science">Natural Sciences</option>
                                <option value="social-science">Social Sciences</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group full-width">
                        <label for="budget">Budget Range (Annual)</label>
                        <div class="budget-slider">
                            <input type="range" id="budget" name="budget" min="0" max="80000" value="40000" step="5000">
                            <div class="budget-display">
                                <span>$0</span>
                                <span id="budgetValue">$40,000</span>
                                <span>$80,000+</span>
                            </div>
                        </div>
                    </div>

                    <button type="submit" class="ai-submit-btn">
                        <i class="fas fa-magic"></i>
                        Find My Perfect Universities
                    </button>
                </form>
            </div>
        </div>
    </section>

    <!-- Manual Search Filters -->
    <section class="manual-search" id="manualSearch" style="display: none;">
        <div class="container">
            <div class="compact-filters">
                <div class="filter-pills">
                    <div class="filter-pill">
                        <select id="country" name="country">
                            <option value="">All Countries</option>
                            <option value="usa">United States</option>
                            <option value="uk">United Kingdom</option>
                            <option value="canada">Canada</option>
                            <option value="australia">Australia</option>
                            <option value="germany">Germany</option>
                            <option value="netherlands">Netherlands</option>
                            <option value="singapore">Singapore</option>
                        </select>
                    </div>

                    <div class="filter-pill">
                        <select id="program" name="program">
                            <option value="">All Programs</option>
                            <option value="bachelor">Bachelor's</option>
                            <option value="master">Master's</option>
                            <option value="phd">PhD</option>
                            <option value="diploma">Diploma</option>
                        </select>
                    </div>

                    <div class="filter-pill">
                        <select id="field" name="field">
                            <option value="">All Fields</option>
                            <option value="engineering">Engineering</option>
                            <option value="computer-science">Computer Science</option>
                            <option value="business">Business</option>
                            <option value="medicine">Medicine</option>
                            <option value="arts">Arts & Humanities</option>
                            <option value="science">Natural Sciences</option>
                            <option value="social-science">Social Sciences</option>
                        </select>
                    </div>

                    <div class="filter-pill">
                        <select id="tuition" name="tuition">
                            <option value="">Any Budget</option>
                            <option value="0-20000">$0 - $20K</option>
                            <option value="20000-40000">$20K - $40K</option>
                            <option value="40000-60000">$40K - $60K</option>
                            <option value="60000+">$60K+</option>
                        </select>
                    </div>

                    <div class="filter-pill">
                        <select id="ranking" name="ranking">
                            <option value="">Any Ranking</option>
                            <option value="top-50">Top 50</option>
                            <option value="top-100">Top 100</option>
                            <option value="top-200">Top 200</option>
                            <option value="top-500">Top 500</option>
                        </select>
                    </div>

                    <button class="search-btn-compact" onclick="searchUniversities()">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Search Results -->
    <section class="search-results">
        <div class="container">
            <div class="results-header">
                <div class="results-info">
                    <h2>Search Results</h2>
                    <span class="results-count">Showing <span id="resultsCount">24</span> universities</span>
                </div>
                <div class="view-controls">
                    <button class="view-btn active" data-view="grid">
                        <i class="fas fa-th-large"></i>
                    </button>
                    <button class="view-btn" data-view="list">
                        <i class="fas fa-list"></i>
                    </button>
                    <button class="compare-btn" onclick="toggleCompareMode()">
                        <i class="fas fa-balance-scale"></i>
                        Compare (<span id="compareCount">0</span>)
                    </button>
                </div>
            </div>
            
            <div class="university-grid" id="universityGrid">
                <!-- University cards will be populated by JavaScript -->
            </div>
        </div>
    </section>

    <!-- Comparison Modal -->
    <div class="comparison-modal" id="comparisonModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>University Comparison</h2>
                <button class="close-btn" onclick="closeComparisonModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="comparison-table-container">
                <table class="comparison-table" id="comparisonTable">
                    <!-- Comparison data will be populated by JavaScript -->
                </table>
            </div>
        </div>
    </div>

    <!-- University Detail Modal -->
    <div class="detail-modal" id="detailModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="detailTitle">University Details</h2>
                <button class="close-btn" onclick="closeDetailModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="detail-content" id="detailContent">
                <!-- University details will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-logo">
                        <i class="fas fa-graduation-cap"></i>
                        <span>StudyAbroad Pro</span>
                    </div>
                    <p>Your trusted partner for achieving study abroad success through comprehensive test preparation, university guidance, and personalized support.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>Services</h4>
                    <ul>
                        <li><a href="index.html#learning-tracker">Learning Tracker</a></li>
                        <li><a href="index.html#admission-guidance">Admission Guidance</a></li>
                        <li><a href="index.html#univ-connect">UnivConnect</a></li>
                        <li><a href="index.html#testimonials">Success Stories</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Test Preparation</h4>
                    <ul>
                        <li><a href="#">GRE Preparation</a></li>
                        <li><a href="#">IELTS Training</a></li>
                        <li><a href="#">TOEFL Courses</a></li>
                        <li><a href="#">Practice Tests</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Contact Info</h4>
                    <div class="contact-info">
                        <div class="contact-item">
                            <i class="fas fa-envelope"></i>
                            <span><EMAIL></span>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-phone"></i>
                            <span>+****************</span>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>123 Education St, Learning City, LC 12345</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 StudyAbroad Pro. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
    <script src="search-script.js"></script>
</body>
</html>
