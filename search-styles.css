/* Search Page Specific Styles */

/* Search Header */
.search-header {
    padding: 8rem 0 4rem;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
    color: white;
    position: relative;
    overflow: hidden;
}

.search-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%);
    opacity: 0.8;
}

.search-header-content {
    text-align: center;
    position: relative;
    z-index: 1;
}

.search-header h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #667eea, #764ba2, #f093fb);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.search-header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* Search Filters */
.search-filters {
    padding: 1.5rem 0;
    background: white;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 10;
}

.filters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.filters-header h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.filter-toggle {
    display: none;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.filter-toggle:hover {
    background: #5a67d8;
}

.toggle-icon {
    transition: transform 0.3s ease;
}

.filters-container.collapsed .toggle-icon {
    transform: rotate(180deg);
}

.filters-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)) auto;
    gap: 1.5rem;
    align-items: end;
    transition: all 0.3s ease;
    overflow: hidden;
}

.filters-container.collapsed {
    max-height: 0;
    opacity: 0;
    margin-bottom: 0;
    padding: 0;
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-group label {
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.filter-group select {
    padding: 0.75rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 0.9rem;
    background: white;
    transition: all 0.3s ease;
}

.filter-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-btn {
    padding: 0.75rem 2rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    height: fit-content;
}

.search-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

/* Search Results */
.search-results {
    padding: 3rem 0;
    background: #f8fafc;
    min-height: 60vh;
    position: relative;
    z-index: 1;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.results-info h2 {
    font-size: 1.8rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.results-count {
    color: #6b7280;
    font-size: 0.9rem;
}

.view-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.view-btn {
    padding: 0.5rem;
    background: white;
    border: 2px solid #e5e7eb;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.view-btn.active,
.view-btn:hover {
    border-color: #667eea;
    color: #667eea;
}

.compare-btn {
    padding: 0.5rem 1rem;
    background: #10b981;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.compare-btn:hover {
    background: #059669;
    transform: translateY(-1px);
}

/* University Grid */
.university-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
}

.university-card {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    position: relative;
}

.university-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.university-card.selected {
    border: 2px solid #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.card-header {
    position: relative;
    height: 200px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="white" opacity="0.2"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
}

.university-logo {
    font-size: 4rem;
    position: relative;
    z-index: 1;
}

.compare-checkbox {
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 24px;
    height: 24px;
    cursor: pointer;
    z-index: 2;
}

.card-content {
    padding: 1.5rem;
}

.university-name {
    font-size: 1.3rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.university-location {
    color: #6b7280;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.university-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.stat-item {
    text-align: center;
    padding: 0.75rem;
    background: #f8fafc;
    border-radius: 8px;
}

.stat-value {
    font-size: 1.1rem;
    font-weight: 700;
    color: #1f2937;
    display: block;
}

.stat-label {
    font-size: 0.8rem;
    color: #6b7280;
}

.program-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.program-tag {
    padding: 0.25rem 0.75rem;
    background: #e0e7ff;
    color: #3730a3;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.card-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-outline {
    flex: 1;
    padding: 0.75rem;
    border: 2px solid #667eea;
    background: transparent;
    color: #667eea;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-outline:hover {
    background: #667eea;
    color: white;
}

/* List View */
.university-grid.list-view {
    grid-template-columns: 1fr;
}

.university-grid.list-view .university-card {
    display: grid;
    grid-template-columns: 200px 1fr;
    height: 200px;
}

.university-grid.list-view .card-header {
    height: 100%;
}

.university-grid.list-view .card-content {
    display: grid;
    grid-template-columns: 1fr auto;
    align-items: center;
    gap: 2rem;
}

.university-grid.list-view .university-stats {
    grid-template-columns: repeat(4, 1fr);
    margin: 0;
}

/* Modals */
.comparison-modal,
.detail-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 2rem;
}

.comparison-modal.active,
.detail-modal.active {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: 16px;
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #6b7280;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: #f3f4f6;
    color: #1f2937;
}

.comparison-table-container {
    overflow: auto;
    flex: 1;
}

.comparison-table {
    width: 100%;
    border-collapse: collapse;
}

.comparison-table th,
.comparison-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
}

.comparison-table th {
    background: #f8fafc;
    font-weight: 600;
    color: #374151;
    position: sticky;
    top: 0;
}

.detail-content {
    padding: 1.5rem;
    overflow: auto;
    flex: 1;
}

.detail-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.detail-section {
    background: #f8fafc;
    padding: 1.5rem;
    border-radius: 12px;
}

.detail-section h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;
    border-bottom: 2px solid #667eea;
    padding-bottom: 0.5rem;
}

.detail-section p {
    margin-bottom: 0.75rem;
    color: #4b5563;
}

.detail-section:last-child {
    grid-column: 1 / -1;
}

.program-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.no-results {
    grid-column: 1 / -1;
    text-align: center;
    padding: 4rem 2rem;
    color: #6b7280;
}

.no-results h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: #374151;
}

/* Animations */
@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* Responsive Design */
@media (max-width: 768px) {
    .search-header h1 {
        font-size: 2rem;
    }

    .filter-toggle {
        display: flex;
    }

    .filters-header h3 {
        font-size: 1.1rem;
    }

    .filters-container {
        grid-template-columns: 1fr;
        max-height: 500px;
    }

    .filters-container.collapsed {
        max-height: 0;
    }

    .results-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .university-grid {
        grid-template-columns: 1fr;
    }

    .university-grid.list-view .university-card {
        grid-template-columns: 1fr;
        height: auto;
    }

    .university-grid.list-view .card-content {
        grid-template-columns: 1fr;
    }

    .modal-content {
        margin: 1rem;
        max-width: calc(100vw - 2rem);
        max-height: calc(100vh - 2rem);
    }

    .detail-grid {
        grid-template-columns: 1fr;
    }
}
