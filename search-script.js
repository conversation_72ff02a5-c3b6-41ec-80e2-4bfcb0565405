// University Search Functionality

// Sample university data
const universities = [
    {
        id: 1,
        name: "Massachusetts Institute of Technology",
        location: "Cambridge, MA, USA",
        country: "usa",
        ranking: 1,
        tuition: 53790,
        costOfLiving: 18000,
        programs: ["Computer Science", "Engineering", "Business"],
        programLevel: ["bachelor", "master", "phd"],
        fields: ["engineering", "computer-science", "business"],
        acceptanceRate: 7,
        averageGPA: 4.17,
        description: "MIT is a world-renowned institution known for its cutting-edge research and innovation in technology and engineering."
    },
    {
        id: 2,
        name: "Stanford University",
        location: "Stanford, CA, USA",
        country: "usa",
        ranking: 2,
        tuition: 56169,
        costOfLiving: 20000,
        programs: ["Computer Science", "Business", "Medicine"],
        programLevel: ["bachelor", "master", "phd"],
        fields: ["computer-science", "business", "medicine"],
        acceptanceRate: 4,
        averageGPA: 4.18,
        description: "Stanford University is a leading research university known for its entrepreneurial spirit and innovation."
    },
    {
        id: 3,
        name: "Harvard University",
        location: "Cambridge, MA, USA",
        country: "usa",
        ranking: 3,
        tuition: 54002,
        costOfLiving: 17000,
        programs: ["Business", "Medicine", "Law"],
        programLevel: ["bachelor", "master", "phd"],
        fields: ["business", "medicine", "arts"],
        acceptanceRate: 5,
        averageGPA: 4.18,
        description: "Harvard University is one of the most prestigious universities in the world, known for its academic excellence."
    },
    {
        id: 4,
        name: "University of Oxford",
        location: "Oxford, UK",
        country: "uk",
        ranking: 4,
        tuition: 35000,
        costOfLiving: 15000,
        programs: ["Arts & Humanities", "Science", "Medicine"],
        programLevel: ["bachelor", "master", "phd"],
        fields: ["arts", "science", "medicine"],
        acceptanceRate: 18,
        averageGPA: 4.0,
        description: "The University of Oxford is the oldest university in the English-speaking world with a rich history of academic excellence."
    },
    {
        id: 5,
        name: "University of Cambridge",
        location: "Cambridge, UK",
        country: "uk",
        ranking: 5,
        tuition: 33000,
        costOfLiving: 14000,
        programs: ["Engineering", "Science", "Arts"],
        programLevel: ["bachelor", "master", "phd"],
        fields: ["engineering", "science", "arts"],
        acceptanceRate: 21,
        averageGPA: 4.0,
        description: "Cambridge University is renowned for its academic excellence and has produced numerous Nobel Prize winners."
    },
    {
        id: 6,
        name: "University of Toronto",
        location: "Toronto, ON, Canada",
        country: "canada",
        ranking: 25,
        tuition: 25000,
        costOfLiving: 12000,
        programs: ["Engineering", "Business", "Medicine"],
        programLevel: ["bachelor", "master", "phd"],
        fields: ["engineering", "business", "medicine"],
        acceptanceRate: 43,
        averageGPA: 3.8,
        description: "University of Toronto is Canada's leading research university with a strong international reputation."
    },
    {
        id: 7,
        name: "University of Melbourne",
        location: "Melbourne, VIC, Australia",
        country: "australia",
        ranking: 33,
        tuition: 30000,
        costOfLiving: 16000,
        programs: ["Business", "Engineering", "Arts"],
        programLevel: ["bachelor", "master", "phd"],
        fields: ["business", "engineering", "arts"],
        acceptanceRate: 70,
        averageGPA: 3.5,
        description: "The University of Melbourne is Australia's leading university with a strong focus on research and innovation."
    },
    {
        id: 8,
        name: "Technical University of Munich",
        location: "Munich, Germany",
        country: "germany",
        ranking: 50,
        tuition: 5000,
        costOfLiving: 10000,
        programs: ["Engineering", "Computer Science", "Science"],
        programLevel: ["bachelor", "master", "phd"],
        fields: ["engineering", "computer-science", "science"],
        acceptanceRate: 25,
        averageGPA: 3.7,
        description: "TUM is one of Europe's leading technical universities with excellent engineering and technology programs."
    }
];

let filteredUniversities = [...universities];
let selectedUniversities = [];
let compareMode = false;

// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    displayUniversities(universities);
    setupEventListeners();
});

// Setup event listeners
function setupEventListeners() {
    // View toggle buttons
    document.querySelectorAll('.view-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            document.querySelectorAll('.view-btn').forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            
            const view = this.dataset.view;
            const grid = document.getElementById('universityGrid');
            
            if (view === 'list') {
                grid.classList.add('list-view');
            } else {
                grid.classList.remove('list-view');
            }
        });
    });
    
    // Filter change listeners
    document.querySelectorAll('select').forEach(select => {
        select.addEventListener('change', searchUniversities);
    });
}

// Search universities based on filters
function searchUniversities() {
    const filters = {
        country: document.getElementById('country').value,
        program: document.getElementById('program').value,
        field: document.getElementById('field').value,
        tuition: document.getElementById('tuition').value,
        ranking: document.getElementById('ranking').value
    };
    
    filteredUniversities = universities.filter(uni => {
        // Country filter
        if (filters.country && uni.country !== filters.country) return false;
        
        // Program level filter
        if (filters.program && !uni.programLevel.includes(filters.program)) return false;
        
        // Field filter
        if (filters.field && !uni.fields.includes(filters.field)) return false;
        
        // Tuition filter
        if (filters.tuition) {
            const [min, max] = filters.tuition.split('-').map(v => parseInt(v.replace('+', '')));
            if (max) {
                if (uni.tuition < min || uni.tuition > max) return false;
            } else {
                if (uni.tuition < min) return false;
            }
        }
        
        // Ranking filter
        if (filters.ranking) {
            const rankingLimit = parseInt(filters.ranking.replace('top-', ''));
            if (uni.ranking > rankingLimit) return false;
        }
        
        return true;
    });
    
    displayUniversities(filteredUniversities);
    updateResultsCount();
}

// Display universities
function displayUniversities(universities) {
    const grid = document.getElementById('universityGrid');
    
    if (universities.length === 0) {
        grid.innerHTML = `
            <div class="no-results">
                <i class="fas fa-search" style="font-size: 3rem; color: #9ca3af; margin-bottom: 1rem;"></i>
                <h3>No universities found</h3>
                <p>Try adjusting your search filters to find more results.</p>
            </div>
        `;
        return;
    }
    
    grid.innerHTML = universities.map(uni => createUniversityCard(uni)).join('');
}

// Create university card HTML
function createUniversityCard(uni) {
    const isSelected = selectedUniversities.includes(uni.id);
    
    return `
        <div class="university-card ${isSelected ? 'selected' : ''}" data-id="${uni.id}">
            <div class="card-header">
                <div class="university-logo">
                    <i class="fas fa-university"></i>
                </div>
                ${compareMode ? `
                    <input type="checkbox" class="compare-checkbox" 
                           ${isSelected ? 'checked' : ''} 
                           onchange="toggleUniversitySelection(${uni.id})">
                ` : ''}
            </div>
            <div class="card-content">
                <h3 class="university-name">${uni.name}</h3>
                <div class="university-location">
                    <i class="fas fa-map-marker-alt"></i>
                    ${uni.location}
                </div>
                <div class="university-stats">
                    <div class="stat-item">
                        <span class="stat-value">#${uni.ranking}</span>
                        <span class="stat-label">World Ranking</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value">$${uni.tuition.toLocaleString()}</span>
                        <span class="stat-label">Tuition/Year</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value">${uni.acceptanceRate}%</span>
                        <span class="stat-label">Acceptance Rate</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value">${uni.averageGPA}</span>
                        <span class="stat-label">Avg GPA</span>
                    </div>
                </div>
                <div class="program-tags">
                    ${uni.programs.slice(0, 3).map(program => 
                        `<span class="program-tag">${program}</span>`
                    ).join('')}
                </div>
                <div class="card-actions">
                    <button class="btn-outline" onclick="showUniversityDetails(${uni.id})">
                        <i class="fas fa-info-circle"></i>
                        Details
                    </button>
                    <button class="btn-outline" onclick="addToCompare(${uni.id})">
                        <i class="fas fa-plus"></i>
                        Compare
                    </button>
                </div>
            </div>
        </div>
    `;
}

// Toggle compare mode
function toggleCompareMode() {
    compareMode = !compareMode;
    const btn = document.querySelector('.compare-btn');
    
    if (compareMode) {
        btn.style.background = '#ef4444';
        btn.innerHTML = '<i class="fas fa-times"></i> Cancel Compare';
    } else {
        btn.style.background = '#10b981';
        btn.innerHTML = `<i class="fas fa-balance-scale"></i> Compare (<span id="compareCount">${selectedUniversities.length}</span>)`;
        selectedUniversities = [];
    }
    
    displayUniversities(filteredUniversities);
}

// Toggle university selection for comparison
function toggleUniversitySelection(id) {
    if (selectedUniversities.includes(id)) {
        selectedUniversities = selectedUniversities.filter(uniId => uniId !== id);
    } else {
        if (selectedUniversities.length < 4) {
            selectedUniversities.push(id);
        } else {
            alert('You can compare up to 4 universities at a time.');
            return;
        }
    }
    
    updateCompareCount();
    displayUniversities(filteredUniversities);
}

// Add university to comparison
function addToCompare(id) {
    if (!selectedUniversities.includes(id)) {
        if (selectedUniversities.length < 4) {
            selectedUniversities.push(id);
            updateCompareCount();
            
            // Show success message
            showNotification('University added to comparison!', 'success');
        } else {
            showNotification('You can compare up to 4 universities at a time.', 'error');
        }
    } else {
        showNotification('University is already in comparison.', 'info');
    }
}

// Update compare count
function updateCompareCount() {
    const countElement = document.getElementById('compareCount');
    if (countElement) {
        countElement.textContent = selectedUniversities.length;
    }
    
    // Enable/disable compare button
    const compareBtn = document.querySelector('.compare-btn');
    if (selectedUniversities.length >= 2) {
        compareBtn.onclick = showComparison;
        compareBtn.innerHTML = `<i class="fas fa-balance-scale"></i> Compare (${selectedUniversities.length})`;
    } else {
        compareBtn.onclick = toggleCompareMode;
    }
}

// Update results count
function updateResultsCount() {
    document.getElementById('resultsCount').textContent = filteredUniversities.length;
}

// Show university details
function showUniversityDetails(id) {
    const uni = universities.find(u => u.id === id);
    const modal = document.getElementById('detailModal');
    const title = document.getElementById('detailTitle');
    const content = document.getElementById('detailContent');
    
    title.textContent = uni.name;
    content.innerHTML = `
        <div class="detail-grid">
            <div class="detail-section">
                <h3>University Information</h3>
                <p><strong>Location:</strong> ${uni.location}</p>
                <p><strong>World Ranking:</strong> #${uni.ranking}</p>
                <p><strong>Acceptance Rate:</strong> ${uni.acceptanceRate}%</p>
                <p><strong>Average GPA:</strong> ${uni.averageGPA}</p>
            </div>
            <div class="detail-section">
                <h3>Financial Information</h3>
                <p><strong>Annual Tuition:</strong> $${uni.tuition.toLocaleString()}</p>
                <p><strong>Cost of Living:</strong> $${uni.costOfLiving.toLocaleString()}</p>
                <p><strong>Total Annual Cost:</strong> $${(uni.tuition + uni.costOfLiving).toLocaleString()}</p>
            </div>
            <div class="detail-section">
                <h3>Programs Offered</h3>
                <div class="program-list">
                    ${uni.programs.map(program => `<span class="program-tag">${program}</span>`).join('')}
                </div>
            </div>
            <div class="detail-section">
                <h3>Description</h3>
                <p>${uni.description}</p>
            </div>
        </div>
    `;
    
    modal.classList.add('active');
}

// Show comparison modal
function showComparison() {
    if (selectedUniversities.length < 2) {
        showNotification('Please select at least 2 universities to compare.', 'error');
        return;
    }
    
    const modal = document.getElementById('comparisonModal');
    const table = document.getElementById('comparisonTable');
    
    const selectedUnis = universities.filter(uni => selectedUniversities.includes(uni.id));
    
    table.innerHTML = `
        <thead>
            <tr>
                <th>Criteria</th>
                ${selectedUnis.map(uni => `<th>${uni.name}</th>`).join('')}
            </tr>
        </thead>
        <tbody>
            <tr>
                <td><strong>Location</strong></td>
                ${selectedUnis.map(uni => `<td>${uni.location}</td>`).join('')}
            </tr>
            <tr>
                <td><strong>World Ranking</strong></td>
                ${selectedUnis.map(uni => `<td>#${uni.ranking}</td>`).join('')}
            </tr>
            <tr>
                <td><strong>Annual Tuition</strong></td>
                ${selectedUnis.map(uni => `<td>$${uni.tuition.toLocaleString()}</td>`).join('')}
            </tr>
            <tr>
                <td><strong>Cost of Living</strong></td>
                ${selectedUnis.map(uni => `<td>$${uni.costOfLiving.toLocaleString()}</td>`).join('')}
            </tr>
            <tr>
                <td><strong>Total Annual Cost</strong></td>
                ${selectedUnis.map(uni => `<td>$${(uni.tuition + uni.costOfLiving).toLocaleString()}</td>`).join('')}
            </tr>
            <tr>
                <td><strong>Acceptance Rate</strong></td>
                ${selectedUnis.map(uni => `<td>${uni.acceptanceRate}%</td>`).join('')}
            </tr>
            <tr>
                <td><strong>Average GPA</strong></td>
                ${selectedUnis.map(uni => `<td>${uni.averageGPA}</td>`).join('')}
            </tr>
            <tr>
                <td><strong>Programs</strong></td>
                ${selectedUnis.map(uni => `<td>${uni.programs.join(', ')}</td>`).join('')}
            </tr>
        </tbody>
    `;
    
    modal.classList.add('active');
}

// Close modals
function closeDetailModal() {
    document.getElementById('detailModal').classList.remove('active');
}

function closeComparisonModal() {
    document.getElementById('comparisonModal').classList.remove('active');
}

// Close modals when clicking outside
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('comparison-modal') || e.target.classList.contains('detail-modal')) {
        e.target.classList.remove('active');
    }
});

// Notification function (reuse from main script)
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(notification => notification.remove());
    
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <span class="notification-message">${message}</span>
            <button class="notification-close">&times;</button>
        </div>
    `;
    
    // Add styles
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#2563eb'};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 10000;
        max-width: 400px;
        animation: slideIn 0.3s ease-out;
    `;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Close button functionality
    notification.querySelector('.notification-close').addEventListener('click', () => {
        notification.remove();
    });
    
    // Auto remove after 3 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 3000);
}
