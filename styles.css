/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 1rem 0;
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: #2563eb;
}

.nav-logo i {
    margin-right: 0.5rem;
    font-size: 1.8rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    align-items: center;
    gap: 2rem;
}

.nav-menu a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-menu a:hover {
    color: #2563eb;
}

.nav-cta {
    background: #2563eb !important;
    color: white !important;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    transition: background 0.3s ease;
}

.nav-cta:hover {
    background: #1d4ed8 !important;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
    color: white;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
    opacity: 0.8;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    position: relative;
    z-index: 1;
}

.hero-content h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.highlight {
    background: linear-gradient(45deg, #667eea, #764ba2, #f093fb);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease-in-out infinite;
}

.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    margin-bottom: 3rem;
}

.btn {
    padding: 1rem 2rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-block;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
}

.btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid white;
}

.btn-secondary:hover {
    background: white;
    color: #667eea;
}

.hero-stats {
    display: flex;
    gap: 2rem;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: #fbbf24;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero-graphic {
    position: relative;
    width: 300px;
    height: 300px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero-graphic > i {
    font-size: 8rem;
    color: rgba(255, 255, 255, 0.2);
    animation: float 3s ease-in-out infinite;
}

.floating-icons {
    position: absolute;
    width: 100%;
    height: 100%;
}

.floating-icons i {
    position: absolute;
    font-size: 2rem;
    color: #fbbf24;
    animation: orbit 8s linear infinite;
}

.floating-icons i:nth-child(1) { animation-delay: 0s; }
.floating-icons i:nth-child(2) { animation-delay: 2s; }
.floating-icons i:nth-child(3) { animation-delay: 4s; }
.floating-icons i:nth-child(4) { animation-delay: 6s; }

/* Services Overview */
.services-overview {
    padding: 6rem 0;
    background: #f8fafc;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1rem;
}

.section-header p {
    font-size: 1.2rem;
    color: #6b7280;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.service-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 2.5rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
    transition: left 0.5s ease;
}

.service-card:hover::before {
    left: 100%;
}

.service-card:hover {
    transform: translateY(-12px) scale(1.02);
    box-shadow: 0 25px 50px rgba(102, 126, 234, 0.2);
    border-color: rgba(102, 126, 234, 0.3);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea, #764ba2, #f093fb);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    position: relative;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
    animation: gradientShift 3s ease-in-out infinite;
}

.service-icon::before {
    content: '';
    position: absolute;
    inset: 2px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 18px;
    backdrop-filter: blur(10px);
}

.service-icon i {
    font-size: 2rem;
    color: white;
    position: relative;
    z-index: 1;
}

.service-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1f2937;
}

.service-card p {
    color: #6b7280;
    margin-bottom: 1.5rem;
}

.service-link {
    color: #2563eb;
    text-decoration: none;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: color 0.3s ease;
}

.service-link:hover {
    color: #1d4ed8;
}

/* Learning Tracker Section */
.learning-tracker {
    padding: 6rem 0;
    background: white;
}

.section-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.section-content.reverse {
    grid-template-columns: 1fr 1fr;
}

.content-text h2 {
    color: #2563eb;
    font-size: 1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 0.5rem;
}

.content-text h3 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1.5rem;
}

.content-text p {
    font-size: 1.1rem;
    color: #6b7280;
    margin-bottom: 2rem;
}

.features-list {
    margin-bottom: 2rem;
}

.feature {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.feature i {
    color: #10b981;
    font-size: 1.2rem;
}

.test-cards {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.test-card {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    display: flex;
    align-items: center;
    gap: 1rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease;
}

.test-card:hover {
    transform: translateX(10px);
}

.test-card.gre {
    border-left: 4px solid #ef4444;
}

.test-card.ielts {
    border-left: 4px solid #3b82f6;
}

.test-card.toefl {
    border-left: 4px solid #10b981;
}

.test-card i {
    font-size: 2rem;
    color: #6b7280;
}

.test-card span {
    font-size: 1.2rem;
    font-weight: 600;
    color: #1f2937;
}

/* Animations */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

@keyframes orbit {
    0% { transform: rotate(0deg) translateX(120px) rotate(0deg); }
    100% { transform: rotate(360deg) translateX(120px) rotate(-360deg); }
}

@keyframes gradientShift {
    0%, 100% {
        background-position: 0% 50%;
        background-size: 200% 200%;
    }
    50% {
        background-position: 100% 50%;
        background-size: 200% 200%;
    }
}

/* Admission Guidance Section */
.admission-guidance {
    padding: 6rem 0;
    background: #f8fafc;
}

.guidance-steps {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.step {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    padding: 1.5rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease;
}

.step:hover {
    transform: translateX(10px);
}

.step-number {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.step-content h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.step-content p {
    color: #6b7280;
    margin: 0;
}

.features-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.feature-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.feature-item i {
    color: #2563eb;
    font-size: 1.5rem;
    margin-top: 0.25rem;
}

.feature-item h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.feature-item p {
    color: #6b7280;
    margin: 0;
    font-size: 0.9rem;
}

/* UnivConnect Section */
.univ-connect {
    padding: 6rem 0;
    background: white;
}

.connect-features {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    margin-bottom: 2rem;
}

.connect-feature {
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #10b981, #059669);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.feature-icon i {
    color: white;
    font-size: 1.5rem;
}

.feature-content h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.feature-content p {
    color: #6b7280;
    margin: 0;
}

.connect-graphic {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2rem;
    position: relative;
}

.profile-node {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 2rem;
    border-radius: 50%;
    text-align: center;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.profile-node i {
    font-size: 3rem;
    margin-bottom: 0.5rem;
    display: block;
}

.connection-lines {
    position: relative;
    height: 100px;
    width: 2px;
}

.line {
    position: absolute;
    width: 2px;
    height: 100px;
    background: linear-gradient(to bottom, #667eea, transparent);
    animation: pulse 2s ease-in-out infinite;
}

.line-1 { left: -50px; animation-delay: 0s; }
.line-2 { left: 0px; animation-delay: 0.5s; }
.line-3 { left: 50px; animation-delay: 1s; }

.university-nodes {
    display: flex;
    gap: 2rem;
}

.uni-node {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.uni-node:hover {
    transform: translateY(-5px);
}

.uni-node i {
    font-size: 2rem;
    color: #6b7280;
    margin-bottom: 0.5rem;
    display: block;
}

.uni-node span {
    font-weight: 600;
    color: #1f2937;
}

/* Testimonials Section */
.testimonials {
    padding: 6rem 0;
    background: #f8fafc;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.testimonial-card {
    background: white;
    padding: 2rem;
    border-radius: 16px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease;
}

.testimonial-card:hover {
    transform: translateY(-5px);
}

.stars {
    display: flex;
    gap: 0.25rem;
    margin-bottom: 1rem;
}

.stars i {
    color: #fbbf24;
}

.testimonial-content p {
    font-style: italic;
    color: #4b5563;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.author-avatar {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.author-info h4 {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.author-info span {
    color: #6b7280;
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        display: none;
    }

    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-content h1 {
        font-size: 2.5rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .hero-stats {
        justify-content: center;
    }

    .section-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .university-nodes {
        flex-direction: column;
        align-items: center;
    }

    .connection-lines {
        transform: rotate(90deg);
        width: 100px;
        height: 2px;
    }

    .line {
        width: 100px;
        height: 2px;
    }
}

/* Registration Section */
.registration {
    padding: 6rem 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.registration-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.registration-info h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
}

.registration-info p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.benefits-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.benefit {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.benefit i {
    color: #10b981;
    font-size: 1.2rem;
}

.registration-form {
    background: white;
    padding: 2.5rem;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.registration-form h3 {
    color: #1f2937;
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 2rem;
    text-align: center;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #2563eb;
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.btn-full {
    width: 100%;
    margin-bottom: 1rem;
}

.form-disclaimer {
    font-size: 0.9rem;
    color: #6b7280;
    text-align: center;
    margin: 0;
}

/* Footer */
.footer {
    background: #1f2937;
    color: white;
    padding: 4rem 0 2rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 3rem;
    margin-bottom: 2rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.footer-logo i {
    margin-right: 0.5rem;
    color: #fbbf24;
}

.footer-section p {
    color: #9ca3af;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: #374151;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: background 0.3s ease;
}

.social-links a:hover {
    background: #2563eb;
}

.footer-section h4 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #fbbf24;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #9ca3af;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: white;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.contact-item i {
    color: #fbbf24;
    width: 20px;
}

.footer-bottom {
    border-top: 1px solid #374151;
    padding-top: 2rem;
    text-align: center;
    color: #9ca3af;
}

/* Additional Animations */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Mobile Navigation */
.hamburger.active span:nth-child(1) {
    transform: rotate(-45deg) translate(-5px, 6px);
}

.hamburger.active span:nth-child(2) {
    opacity: 0;
}

.hamburger.active span:nth-child(3) {
    transform: rotate(45deg) translate(-5px, -6px);
}

.nav-menu.active {
    display: flex !important;
    position: fixed;
    top: 80px;
    left: 0;
    width: 100%;
    height: calc(100vh - 80px);
    background: white;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding-top: 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 999;
}

.nav-menu.active li {
    margin: 1rem 0;
}

.nav-menu.active a {
    font-size: 1.2rem;
}

/* Additional Responsive Styles */
@media (max-width: 768px) {
    .registration-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .social-links {
        justify-content: center;
    }

    .hero-graphic {
        width: 250px;
        height: 250px;
    }

    .hero-graphic > i {
        font-size: 6rem;
    }

    .floating-icons i {
        font-size: 1.5rem;
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
    }

    .guidance-steps {
        gap: 1rem;
    }

    .step {
        padding: 1rem;
    }

    .connect-features {
        gap: 1.5rem;
    }

    .connect-feature {
        flex-direction: column;
        text-align: center;
    }
}
